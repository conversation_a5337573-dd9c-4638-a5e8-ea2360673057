# HXBK JSON序列化字段名问题修复文档

## 问题描述

在HXBK用信申请的JSON序列化过程中，发现报文中同时出现了`m_type`和`mtype`两个字段，而预期应该只有`m_type`字段。

### 问题报文示例
```
"materials.1.m_type":"2","materials.1.big_code":"20","fund_code":"D20250701000000001","materials.2.m_type":"2","materials.3.m_type":"2","materials.2.mtype":"2","borrower_emp_info.company_address":"南京市鼓楼区中山路321
```

可以看到同时出现了：
- `materials.2.m_type":"2"` (正确)
- `materials.2.mtype":"2"` (错误)

## 问题根源分析

### 1. 字段定义
在`HXBKLoanApplyRequest.java`的内部类`Material`中，字段定义是正确的：

```java
/**
 * 资料类型
 * 授信阶段
 * 0-风控报告
 * 1-合同
 * 2-图片
 * 3-附件
 *
 * 用信阶段
 * 4-风控报告
 * 5-合同
 * 6-图片
 * 7-附件
 */
@JsonProperty("m_type")
private String mType;
```

### 2. 问题所在
问题出现在getter/setter方法的命名上：

**错误的方法命名：**
```java
public String getmType() {  // 错误：应该是getMType()
    return mType;
}

public void setmType(String mType) {  // 错误：应该是setMType()
    this.mType = mType;
}
```

### 3. 问题原理
- JSON序列化器（如FastJSON）在处理JavaBean时，会同时考虑`@JsonProperty`注解和getter方法名
- 错误的getter方法名`getmType()`被解析为属性名`mtype`（去掉get前缀，首字母小写）
- 这导致JSON输出中同时出现：
  - `m_type`：来自`@JsonProperty("m_type")`注解
  - `mtype`：来自错误的getter方法名`getmType()`

## 解决方案

### 修复方法
将getter/setter方法名修正为符合JavaBean规范的命名：

**修复后的正确命名：**
```java
public String getMType() {  // 正确：M大写
    return mType;
}

public void setMType(String mType) {  // 正确：M大写
    this.mType = mType;
}
```

### 修复位置
文件：`capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/dto/loan/HXBKLoanApplyRequest.java`
行数：350-356

## 修复验证

修复后，JSON序列化将只输出`m_type`字段，不再出现多余的`mtype`字段。

## 经验总结

1. **JavaBean命名规范**：getter/setter方法必须严格遵循JavaBean命名规范
   - getter方法：`get` + 首字母大写的属性名
   - setter方法：`set` + 首字母大写的属性名

2. **JSON注解与方法名的关系**：
   - `@JsonProperty`注解指定JSON字段名
   - 错误的getter/setter方法名可能导致额外的字段被序列化

3. **代码审查要点**：
   - 检查getter/setter方法命名是否符合JavaBean规范
   - 确保`@JsonProperty`注解与实际需要的JSON字段名一致
   - 避免同一个属性产生多个JSON字段

## 相关文件

- `HXBKLoanApplyRequest.java` - 修复了Material内部类的getter/setter方法命名
- `HXBKMaterial.java` - 该类的getter/setter方法命名是正确的，可作为参考

## 修复日期
2025-07-28
